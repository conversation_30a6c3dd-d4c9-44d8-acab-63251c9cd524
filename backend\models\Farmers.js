import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const farmerSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  mobileNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
password: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: String,
    // enum: ['FARMER'],
    default: 'FARMER',
  },
  category: {
    type: String,
    required: false,
    trim: true
  },
  skills: [{
    type: String,
    trim: true
  }],
  profilePhoto: {
    type: String,
    default: null
  },
 location: {
    type: {
      type: String,
      default: 'Point',
      enum: ['Point']
    },
    coordinates: {
      type: [Number],
      required: true,
      validate: {
        validator: function(v) {
          return v.length === 2;
        },
        message: 'Coordinates must be an array of [longitude, latitude]'
      }
    }
  }
}, {
  timestamps: true
});


farmerSchema.index({ location: '2dsphere' });
farmerSchema.index({ category: 1 });
farmerSchema.index({ role: 1 });

const Farmer = mongoose.model('Farmer', farmerSchema);

export default Farmer;
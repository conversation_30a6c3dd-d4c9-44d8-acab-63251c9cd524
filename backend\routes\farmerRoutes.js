import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import Farmer from '../models/Farmers.js';
import { auth } from '../middleware/auth.js';

const router = express.Router();

// Register a new farmer
router.post('/register', async (req, res) => {
  console.log(' before Registration request:', req.body);

  try {
    console.log('Registration request:', req.body);
    const { name, email, mobileNumber, password, skills, location } = req.body;

    // Check if farmer already exists
    const existingFarmer = await Farmer.findOne({
      $or: [
        { email: email.toLowerCase() },
        { mobileNumber }
      ]
    });

    if (existingFarmer) {
      return res.status(400).json({
        error: 'A user with this email or mobile number already exists'
      });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new farmer
    const farmer = new Farmer({
      name,
      email: email.toLowerCase(),
      mobileNumber,
      password: hashedPassword,
      role: 'FARMER',
      skills: skills || [],
      location: location || {
        type: 'Point',
        coordinates: [0, 0]
      }
    });

    await farmer.save();

    const token = generateToken(user._id);

    // Return user data and token
    res.status(201).json({
      user: sanitizeUser(user),
      token
    });
    const farmerResponse = farmer.toObject();
    delete farmerResponse.password;

    res.status(201).json({
      token,
      user: farmerResponse
    });

  } catch (error) {
    console.error('Error in farmer registration:', error);
    res.status(500).json({
      error: error.message || 'An error occurred during registration'
    });
  }
});

// Login farmer
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find farmer by email
    const farmer = await Farmer.findOne({ email: email.toLowerCase() }).select('+password');
    if (!farmer) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const isMatch = await bcrypt.compare(password, farmer.password);
    if (!isMatch) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: farmer._id },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Remove password from response
    const farmerResponse = farmer.toObject();
    delete farmerResponse.password;

    res.json({
      token,
      user: farmerResponse
    });

  } catch (error) {
    console.error('Error in farmer login:', error);
    res.status(500).json({
      error: error.message || 'An error occurred during login'
    });
  }
});

// Get farmer profile
router.get('/profile', auth, async (req, res) => {
  try {
    const farmer = await Farmer.findById(req.user._id);
    if (!farmer) {
      return res.status(404).json({ error: 'Farmer not found' });
    }
    res.json(farmer);
  } catch (error) {
    console.error('Error fetching farmer profile:', error);
    res.status(500).json({
      error: error.message || 'Error fetching profile'
    });
  }
});

// Update farmer profile
router.patch('/profile', auth, async (req, res) => {
  try {
    const updates = req.body;
    const allowedUpdates = ['name', 'skills', 'location'];
    const isValidOperation = Object.keys(updates).every(update => 
      allowedUpdates.includes(update)
    );

    if (!isValidOperation) {
      return res.status(400).json({ error: 'Invalid updates' });
    }

    const farmer = await Farmer.findById(req.user._id);
    if (!farmer) {
      return res.status(404).json({ error: 'Farmer not found' });
    }

    Object.keys(updates).forEach(update => {
      farmer[update] = updates[update];
    });

    await farmer.save();
    res.json(farmer);
  } catch (error) {
    console.error('Error updating farmer profile:', error);
    res.status(500).json({
      error: error.message || 'Error updating profile'
    });
  }
});

// Get nearby farmers
router.get('/nearby', async (req, res) => {
  try {
    const { longitude, latitude, maxDistance = 10000 } = req.query; // maxDistance in meters, default 10km

    const farmers = await Farmer.find({
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(longitude), parseFloat(latitude)]
          },
          $maxDistance: parseInt(maxDistance)
        }
      }
    }).select('-password');

    res.json(farmers);
  } catch (error) {
    console.error('Error finding nearby farmers:', error);
    res.status(500).json({
      error: error.message || 'Error finding nearby farmers'
    });
  }
});

export default router;
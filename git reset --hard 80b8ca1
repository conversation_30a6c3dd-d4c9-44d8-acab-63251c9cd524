[33m77e40f2[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32msm2[m[33m)[m HEAD@{0}: reset: moving to head~2
[33m3771020[m HEAD@{1}: reset: moving to head~1
[33m80b8ca0[m[33m ([m[1;31morigin/sm2[m[33m)[m HEAD@{2}: checkout: moving from main to sm2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{3}: checkout: moving from sm2 to main
[33m80b8ca0[m[33m ([m[1;31morigin/sm2[m[33m)[m HEAD@{4}: checkout: moving from main to sm2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{5}: checkout: moving from sm2 to main
[33m80b8ca0[m[33m ([m[1;31morigin/sm2[m[33m)[m HEAD@{6}: commit: fixed provider issue
[33m3771020[m HEAD@{7}: commit: fixing swithing inner tab
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{8}: checkout: moving from m2 to sm2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{9}: checkout: moving from sm1 to m2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{10}: checkout: moving from m2 to sm1
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{11}: checkout: moving from main to m2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{12}: checkout: moving from m2 to main
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{13}: checkout: moving from main to m2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{14}: checkout: moving from m1 to main
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{15}: checkout: moving from m2 to m1
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{16}: checkout: moving from main to m2
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{17}: merge m1: Fast-forward
[33m77e40f2[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32msm2[m[33m)[m HEAD@{18}: checkout: moving from m1 to main
[33mcce5b6f[m[33m ([m[1;31morigin/main[m[33m, [m[1;31morigin/m2[m[33m, [m[1;31morigin/m1[m[33m, [m[1;31morigin/HEAD[m[33m, [m[1;32msm1[m[33m, [m[1;32mmain[m[33m, [m[1;32mm2[m[33m, [m[1;32mm1[m[33m)[m HEAD@{19}: commit: review added
[33m77e40f2[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32msm2[m[33m)[m HEAD@{20}: checkout: moving from main to m1
[33m77e40f2[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32msm2[m[33m)[m HEAD@{21}: clone: from https://github.com/sandeepshegane1/localtask.git

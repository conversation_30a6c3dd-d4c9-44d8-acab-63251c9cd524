{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"vite\" \"node --watch backend/server.js\"", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node --watch backend/server.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^6.3.1", "@mui/material": "^6.3.1", "@react-google-maps/api": "^2.20.5", "@tanstack/react-query": "^5.62.3", "@types/leaflet": "^1.9.16", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "framer-motion": "^11.13.1", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.344.0", "mongoose": "^8.2.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.16", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.4.1", "react-leaflet": "^5.0.0", "react-router-dom": "^6.22.2", "sonner": "^1.7.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "validator": "^13.12.0", "zod": "^3.22.4", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.10.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "shadcn-ui": "^0.9.4", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}
import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { UserCircle, Menu, X, Home, LayoutDashboard, LogOut } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { motion, AnimatePresence } from 'framer-motion';

export function Navbar() {
  const { isAuthenticated, logout } = useAuthStore();
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  return (
    <nav className="bg-white shadow sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex-shrink-0 flex items-center">
              <motion.span
                className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-indigo-600"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                LocalTasker
              </motion.span>
            </Link>
          </div>

          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-1">
            {isAuthenticated ? (
              <>
                <Link
                  to="/dashboard"
                  className="group relative px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  <span className="relative z-10 flex items-center text-gray-700 group-hover:text-purple-600">
                    <LayoutDashboard className="w-5 h-5 mr-1" />
                    Dashboard
                  </span>
                  <motion.span
                    className="absolute inset-0 bg-purple-50 rounded-md z-0"
                    initial={{ scale: 0, opacity: 0 }}
                    whileHover={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </Link>
                <Link
                  to="/profile"
                  className="group relative px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  <span className="relative z-10 flex items-center text-gray-700 group-hover:text-purple-600">
                    <UserCircle className="w-5 h-5 mr-1" />
                    Profile
                  </span>
                  <motion.span
                    className="absolute inset-0 bg-purple-50 rounded-md z-0"
                    initial={{ scale: 0, opacity: 0 }}
                    whileHover={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </Link>
                <button
                  onClick={handleLogout}
                  className="group relative ml-2 px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  <span className="relative z-10 flex items-center text-gray-700 group-hover:text-purple-600">
                    <LogOut className="w-5 h-5 mr-1" />
                    Logout
                  </span>
                  <motion.span
                    className="absolute inset-0 bg-purple-50 rounded-md z-0"
                    initial={{ scale: 0, opacity: 0 }}
                    whileHover={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </button>
              </>
            ) : (
              <>
                <Link
                  to="/login"
                  className="group relative px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  <span className="relative z-10 text-gray-700 group-hover:text-purple-600">
                    Login
                  </span>
                  <motion.span
                    className="absolute inset-0 bg-purple-50 rounded-md z-0"
                    initial={{ scale: 0, opacity: 0 }}
                    whileHover={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                </Link>
                <Link
                  to="/register"
                  className="ml-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:from-purple-700 hover:to-indigo-700 px-4 py-2 rounded-md text-sm font-medium shadow-sm hover:shadow transition-all duration-300"
                >
                  Register
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-purple-600 hover:bg-purple-50 focus:outline-none"
            >
              {isMobileMenuOpen ? (
                <X className="block h-6 w-6" />
              ) : (
                <Menu className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="md:hidden bg-white shadow-lg"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {isAuthenticated ? (
                <>
                  <Link
                    to="/dashboard"
                    className="flex items-center text-gray-700 hover:bg-purple-50 hover:text-purple-600 px-3 py-2 rounded-md text-base font-medium"
                  >
                    <LayoutDashboard className="w-5 h-5 mr-2" />
                    Dashboard
                  </Link>
                  <Link
                    to="/profile"
                    className="flex items-center text-gray-700 hover:bg-purple-50 hover:text-purple-600 px-3 py-2 rounded-md text-base font-medium"
                  >
                    <UserCircle className="w-5 h-5 mr-2" />
                    Profile
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full text-left text-gray-700 hover:bg-purple-50 hover:text-purple-600 px-3 py-2 rounded-md text-base font-medium"
                  >
                    <LogOut className="w-5 h-5 mr-2" />
                    Logout
                  </button>
                </>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="text-gray-700 hover:bg-purple-50 hover:text-purple-600 block px-3 py-2 rounded-md text-base font-medium"
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white block px-3 py-2 rounded-md text-base font-medium"
                  >
                    Register
                  </Link>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </nav>
  );
}
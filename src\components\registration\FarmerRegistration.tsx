import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { useAuthStore } from '../../stores/authStore';
import { MapPin, Loader } from 'lucide-react';
import { FARMERS_SERVICES } from '../../constants/farmers';

const FarmerRegistration = () => {
  const { register: registerUser } = useAuthStore();
  const navigate = useNavigate();
  const { 
    register, 
    handleSubmit, 
    formState: { errors } 
  } = useForm({
    defaultValues: {
      name: '',
      email: '',
      password: '',
      mobileNumber: ''
    }
  });
  
  const [step, setStep] = useState(1);
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [location, setLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  const handleSkillToggle = (skill: string) => {
    setSelectedSkills(prev => 
      prev.includes(skill)
        ? prev.filter(s => s !== skill)
        : [...prev, skill]
    );
  };

  const getCurrentLocation = () => {
    setIsGettingLocation(true);
    setLocationError(null);

    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          };
          setLocation(newLocation);
          setIsGettingLocation(false);
          console.log('Location set:', newLocation);
        },
        (error) => {
          console.error("Error getting location:", error);
          setLocationError("Failed to get your location. Please try again.");
          setIsGettingLocation(false);
        }
      );
    } else {
      setLocationError("Geolocation is not supported by your browser.");
      setIsGettingLocation(false);
    }
  };

  const onSubmit = async (data: any) => {
    if (step === 1) {
      if (!data.name || !data.email || !data.password || !data.mobileNumber) {
        toast.error('Please fill all required fields');
        return;
      }
      setStep(2);
      return;
    }

    if (step === 2) {
      if (selectedSkills.length === 0) {
        toast.error('Please select at least one farming skill');
        return;
      }
      setStep(3);
      return;
    }

    if (!location) {
      toast.error('Please provide your location');
      return;
    }

    try {
      const userData = {
        ...data,
        role: 'FARMER',
        skills: selectedSkills,
        location: {
          type: 'Point',
          coordinates: [location.longitude, location.latitude]
        }
      };

      console.log('Sending farmer data:', userData);
      await registerUser(userData);
      toast.success('Registration successful!');
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.response?.data?.error || 'An error occurred during registration');
    }
  };

  const renderFirstStep = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Full Name</label>
        <input
          {...register('name', { 
            required: 'Name is required',
            minLength: { value: 2, message: 'Name must be at least 2 characters' }
          })}
          type="text"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500"
        />
        {errors.name && <p className="text-red-500 text-xs">{errors.name.message as string}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Email</label>
        <input
          {...register('email', { 
            required: 'Email is required',
            pattern: {
              value: /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
              message: 'Invalid email address'
            }
          })}
          type="email"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500"
        />
        {errors.email && <p className="text-red-500 text-xs">{errors.email.message as string}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Mobile Number</label>
        <input
          {...register('mobileNumber', { 
            required: 'Mobile number is required',
            pattern: {
              value: /^[0-9]{10}$/,
              message: 'Invalid mobile number',
            }
          })}
          type="tel"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500"
        />
        {errors.mobileNumber && <p className="text-red-500 text-xs">{errors.mobileNumber.message as string}</p>}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Password</label>
        <input
          {...register('password', { 
            required: 'Password is required',
            minLength: { value: 6, message: 'Password must be at least 6 characters' }
          })}
          type="password"
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-green-500"
        />
        {errors.password && <p className="text-red-500 text-xs">{errors.password.message as string}</p>}
      </div>
    </div>
  );

  const renderSkillsSelection = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-4">Select Your Farming Skills</h2>
      <div className="grid grid-cols-2 gap-4">
        {FARMERS_SERVICES.map((item) => (
          <div
            key={item.category}
            onClick={() => handleSkillToggle(item.category)}
            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
              selectedSkills.includes(item.category)
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 hover:border-green-200'
            }`}
          >
            <div className="flex items-center space-x-3">
              <item.icon className="w-6 h-6 text-green-600" />
              <div>
                <h3 className="font-medium">{item.title}</h3>
                <p className="text-sm text-gray-500">{item.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderLocationStep = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-4">Provide Your Farm Location</h2>
      <div className="flex items-center justify-center space-x-4">
        <button
          type="button"
          onClick={getCurrentLocation}
          disabled={isGettingLocation}
          className="flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          {isGettingLocation ? (
            <>
              <Loader className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
              Getting Location...
            </>
          ) : (
            <>
              <MapPin className="-ml-1 mr-2 h-5 w-5" />
              Get Farm Location
            </>
          )}
        </button>
      </div>
      {location && (
        <div className="text-center text-sm text-gray-600">
          Location set: {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
        </div>
      )}
      {locationError && (
        <p className="text-center text-red-500 text-sm">{locationError}</p>
      )}
    </div>
  );

  return (
    <div className="max-w-md mx-auto p-6 bg-white shadow-md rounded-lg">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {step === 1 && renderFirstStep()}
        {step === 2 && renderSkillsSelection()}
        {step === 3 && renderLocationStep()}
        
        <button
          type="submit"
          className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
        >
          {step === 1 ? 'Next' : step === 2 ? 'Next' : 'Complete Registration'}
        </button>
      </form>
    </div>
  );
};

export default FarmerRegistration;
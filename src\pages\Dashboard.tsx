import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import { Services } from '../components/dashboard/Services';
import { Workers } from '../components/dashboard/Workers';
import { ServiceProviders } from '../components/dashboard/ServiceProviders';
import { WorkersByCategory } from '../components/dashboard/WorkersByCategory';
import { ProviderDashboard } from '../components/dashboard/ProviderDashboard';
import { DashboardNav } from '../components/dashboard/DashboardNav';
import { Farmers } from '../components/dashboard/Farmers';
import { FarmersActivity } from '../components/dashboard/FarmersActivity';
// import { FarmerDashboard } from '../components/dashboard/FarmerDashboard';
import { motion } from 'framer-motion';

export function Dashboard() {
  const user = useAuthStore((state) => state.user);

  if (!user) {
    return <Navigate to="/login" />;
  }

  // Function to determine user type based on object structure
  const getUserType = (user: any) => {
    if (user.professionalProfile) {
      return 'FARMER';
    } else if (user.services) {
      return 'PROVIDER';
    } else if (user.role) {
      return user.role;
    } else {
      return 'CLIENT';
    }
  };

  const userType = getUserType(user);

  // Get user's name based on user type
  const getUserName = () => {
    if (userType === 'FARMER' && user.professionalProfile?.name) {
      return `${user.professionalProfile.name.firstName} ${user.professionalProfile.name.lastName || ''}`;
    } else if (user.name) {
      return user.name;
    } else {
      return 'User';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <DashboardNav userType={userType} />
      <motion.div
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mb-8">
          <motion.h1
            className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-indigo-600"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Welcome, {getUserName()}!
          </motion.h1>
          <motion.div
            className="h-1 w-20 bg-gradient-to-r from-purple-600 to-indigo-600 rounded mt-2"
            initial={{ width: 0 }}
            animate={{ width: 80 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          />
        </div>

        {(userType === 'CLIENT' || userType === 'client') && (
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard/services" replace />} />
            <Route path="/services" element={<Services />} />
            <Route path="/services/:category/providers" element={<ServiceProviders />} />
            <Route path="/workers" element={<Workers />} />
            <Route path="/workers/:category" element={<WorkersByCategory />} />
            <Route path="/farmers" element={<Farmers />} />
            <Route path="/farmers/:category" element={<FarmersActivity />} />
          </Routes>
        )}

        {(userType === 'FARMER' || userType === 'farmer') && (
          <Routes>
            <Route path="/*" element={<FarmerDashboard />} />
          </Routes>
        )}

        {(userType === 'PROVIDER' || userType === 'provider') && (
          <Routes>
            <Route path="/*" element={<ProviderDashboard />} />
          </Routes>
        )}
      </motion.div>
    </div>
  );
}

import React, { useState } from 'react';
import { ClientRegistration } from '../components/registration/ClientRegistration';
import { ProviderRegistration } from '../components/registration/ProviderRegistration';
import FarmerRegistration from '../components/registration/FarmerRegistration';
import { motion } from 'framer-motion';
import { ArrowLeft } from 'lucide-react';

export function Register() {
  const [selectedRole, setSelectedRole] = useState<'CLIENT' | 'PROVIDER' | 'FARMER' | null>(null);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div
        className="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-lg"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <motion.h2
            className="mt-2 text-center text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-indigo-600"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            Create your account
          </motion.h2>
          <motion.p
            className="mt-2 text-center text-sm text-gray-600"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Join LocalTasker and connect with local services
          </motion.p>
        </div>

        {!selectedRole ? (
          <motion.div
            className="space-y-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <motion.button
              onClick={() => setSelectedRole('CLIENT')}
              className="w-full flex justify-center py-3 px-4 border-2 border-purple-600 rounded-md text-sm font-medium text-purple-600 bg-white hover:bg-purple-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300"
              whileHover={{ scale: 1.02, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              whileTap={{ scale: 0.98 }}
            >
              Register as Client
            </motion.button>
            <motion.button
              onClick={() => setSelectedRole('PROVIDER')}
              className="w-full flex justify-center py-3 px-4 border-2 border-purple-600 rounded-md text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300 shadow-sm"
              whileHover={{ scale: 1.02, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              whileTap={{ scale: 0.98 }}
            >
              Register as Service Provider
            </motion.button>
            {/* <motion.button
              onClick={() => setSelectedRole('FARMER')}
              className="w-full flex justify-center py-3 px-4 border-2 border-purple-600 rounded-md text-sm font-medium text-white bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300 shadow-sm hover:shadow"
              whileHover={{ scale: 1.02, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              whileTap={{ scale: 0.98 }}
            >
              Register as Farmer
            </motion.button> */}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            {selectedRole === 'CLIENT' ? (
              <ClientRegistration />
            ) : selectedRole === 'PROVIDER' ? (
              <ProviderRegistration />
            ) : (
              <FarmerRegistration />
            )}
            <motion.button
              onClick={() => setSelectedRole(null)}
              className="mt-6 flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-300 mx-auto"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Role Selection
            </motion.button>
          </motion.div>
        )}
      </motion.div>
    </div>
  );
}
